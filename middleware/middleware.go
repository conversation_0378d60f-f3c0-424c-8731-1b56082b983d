package middleware

import (
	"fmt"
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		// 获取查询参数信息
		queryInfo := ""
		if param.Request.URL.RawQuery != "" {
			queryInfo = fmt.Sprintf(" Query: %s", param.Request.URL.RawQuery)
		}

		// 获取认证相关头信息
		authInfo := ""
		if auth := param.Request.Header.Get("Authorization"); auth != "" {
			authInfo += fmt.Sprintf(" Auth: %s", auth[:min(len(auth), 20)]+"...")
		}
		if apiKey := param.Request.Header.Get("X-API-Key"); apiKey != "" {
			authInfo += fmt.Sprintf(" X-API-Key: %s", apiKey[:min(len(apiKey), 10)]+"...")
		}

		return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\"%s%s %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format(time.RFC1123),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			queryInfo,
			authInfo,
			param.ErrorMessage,
		)
	})
}

// min 辅助函数
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// Recovery 恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			log.Printf("Panic recovered: %s", err)
			c.JSON(500, gin.H{
				"error": gin.H{
					"message": "Internal server error",
					"type":    "internal_error",
				},
			})
		}
		c.AbortWithStatus(500)
	})
}

// RateLimiter 简单的速率限制中间件（基于IP）
func RateLimiter() gin.HandlerFunc {
	// 这里可以实现更复杂的速率限制逻辑
	// 目前只是一个占位符
	return func(c *gin.Context) {
		c.Next()
	}
}

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 简单的请求ID生成（基于时间戳）
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// AuthMiddleware 认证中间件 - 兼容Google Gemini API的key参数方式
func AuthMiddleware(authKey string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果没有配置认证密钥，跳过认证
		if authKey == "" {
			c.Next()
			return
		}

		// 从多个地方获取认证密钥，优先使用Gemini API的方式
		var clientKey string
		var keySource string

		// 1. 从查询参数key获取（Gemini API标准方式）
		clientKey = c.Query("key")
		if clientKey != "" {
			keySource = "query-key"
		}

		// 2. 从查询参数auth_key获取（备用方式）
		if clientKey == "" {
			clientKey = c.Query("auth_key")
			if clientKey != "" {
				keySource = "query-auth_key"
			}
		}

		// 3. 从Authorization头获取 (Bearer token格式)
		if clientKey == "" {
			authHeader := c.GetHeader("Authorization")
			if authHeader != "" {
				if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
					clientKey = authHeader[7:]
					keySource = "header-authorization"
				}
			}
		}

		// 4. 从X-API-Key头获取
		if clientKey == "" {
			clientKey = c.GetHeader("X-API-Key")
			if clientKey != "" {
				keySource = "header-x-api-key"
			}
		}

		// 调试信息
		log.Printf("Auth check - Path: %s, Query: %s, Key source: %s, Key found: %t",
			c.Request.URL.Path,
			c.Request.URL.RawQuery,
			keySource,
			clientKey != "")

		// 验证密钥
		if clientKey != authKey {
			c.JSON(401, gin.H{
				"error": gin.H{
					"message": "Invalid API key provided. Please check your authentication key.",
					"type":    "invalid_request_error",
					"code":    "invalid_api_key",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
