package middleware

import (
	"fmt"
	"log"
	"time"

	"github.com/gin-gonic/gin"
)

// Logger 日志中间件
func Logger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format(time.RFC1123),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	})
}

// CORS 跨域中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.<PERSON>er("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		c.<PERSON>("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// Recovery 恢复中间件
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			log.Printf("Panic recovered: %s", err)
			c.JSON(500, gin.H{
				"error": gin.H{
					"message": "Internal server error",
					"type":    "internal_error",
				},
			})
		}
		c.AbortWithStatus(500)
	})
}

// RateLimiter 简单的速率限制中间件（基于IP）
func RateLimiter() gin.HandlerFunc {
	// 这里可以实现更复杂的速率限制逻辑
	// 目前只是一个占位符
	return func(c *gin.Context) {
		c.Next()
	}
}

// RequestID 请求ID中间件
func RequestID() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}
		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)
		c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 简单的请求ID生成（基于时间戳）
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// AuthMiddleware 认证中间件
func AuthMiddleware(authKey string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 如果没有配置认证密钥，跳过认证
		if authKey == "" {
			c.Next()
			return
		}

		// 从多个地方获取认证密钥
		var clientKey string

		// 1. 从Authorization头获取 (Bearer token格式)
		authHeader := c.GetHeader("Authorization")
		if authHeader != "" {
			if len(authHeader) > 7 && authHeader[:7] == "Bearer " {
				clientKey = authHeader[7:]
			}
		}

		// 2. 从X-API-Key头获取
		if clientKey == "" {
			clientKey = c.GetHeader("X-API-Key")
		}

		// 3. 从查询参数获取
		if clientKey == "" {
			clientKey = c.Query("auth_key")
		}

		// 验证密钥
		if clientKey != authKey {
			c.JSON(401, gin.H{
				"error": gin.H{
					"message": "Invalid authentication key",
					"type":    "authentication_error",
					"code":    "invalid_auth_key",
				},
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
