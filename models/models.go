package models

import "time"

// OpenAIRequest OpenAI API请求结构（简化版，支持主要字段）
type OpenAIRequest struct {
	Model            string                 `json:"model"`
	Messages         []Message              `json:"messages,omitempty"`
	Prompt           string                 `json:"prompt,omitempty"`
	MaxTokens        *int                   `json:"max_tokens,omitempty"`
	Temperature      *float64               `json:"temperature,omitempty"`
	TopP             *float64               `json:"top_p,omitempty"`
	N                *int                   `json:"n,omitempty"`
	Stream           *bool                  `json:"stream,omitempty"`
	Stop             interface{}            `json:"stop,omitempty"`
	PresencePenalty  *float64               `json:"presence_penalty,omitempty"`
	FrequencyPenalty *float64               `json:"frequency_penalty,omitempty"`
	LogitBias        map[string]interface{} `json:"logit_bias,omitempty"`
	User             string                 `json:"user,omitempty"`
}

// Message 消息结构
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
	Name    string `json:"name,omitempty"`
}

// OpenAIResponse OpenAI API响应结构（简化版）
type OpenAIResponse struct {
	ID      string   `json:"id"`
	Object  string   `json:"object"`
	Created int64    `json:"created"`
	Model   string   `json:"model"`
	Choices []Choice `json:"choices"`
	Usage   Usage    `json:"usage"`
}

// Choice 选择结构
type Choice struct {
	Index        int     `json:"index"`
	Message      Message `json:"message,omitempty"`
	Text         string  `json:"text,omitempty"`
	FinishReason string  `json:"finish_reason"`
}

// Usage 使用情况结构
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Error ErrorDetail `json:"error"`
}

// ErrorDetail 错误详情
type ErrorDetail struct {
	Message string `json:"message"`
	Type    string `json:"type"`
	Code    string `json:"code,omitempty"`
}

// ProxyStats 代理统计信息
type ProxyStats struct {
	TotalRequests    int64                    `json:"total_requests"`
	SuccessRequests  int64                    `json:"success_requests"`
	FailedRequests   int64                    `json:"failed_requests"`
	AverageLatency   float64                  `json:"average_latency_ms"`
	KeyStats         map[string]*KeyStatsInfo `json:"key_stats"`
	Uptime           string                   `json:"uptime"`
	LastUpdated      time.Time                `json:"last_updated"`
}

// KeyStatsInfo 密钥统计信息（用于API响应）
type KeyStatsInfo struct {
	TotalRequests   int64     `json:"total_requests"`
	SuccessRequests int64     `json:"success_requests"`
	FailedRequests  int64     `json:"failed_requests"`
	SuccessRate     float64   `json:"success_rate"`
	LastUsed        time.Time `json:"last_used"`
	IsHealthy       bool      `json:"is_healthy"`
}

// HealthResponse 健康检查响应
type HealthResponse struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version"`
	Uptime    string    `json:"uptime"`
}
