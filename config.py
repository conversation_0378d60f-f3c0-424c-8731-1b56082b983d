"""
配置管理模块
"""
import os
from typing import List, Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置"""
    
    # 服务配置
    host: str = Field(default="0.0.0.0", description="服务监听地址")
    port: int = Field(default=8000, description="服务监听端口")
    
    # API密钥配置 - 支持多个密钥，用逗号分隔
    api_keys: str = Field(description="API密钥列表，用逗号分隔")
    
    # 目标API服务配置
    target_base_url: str = Field(
        default="https://api.openai.com",
        description="目标API服务的基础URL"
    )
    
    # 代理配置
    timeout: int = Field(default=60, description="请求超时时间（秒）")
    max_retries: int = Field(default=3, description="最大重试次数")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    def get_api_keys(self) -> List[str]:
        """获取API密钥列表"""
        if not self.api_keys:
            raise ValueError("API_KEYS environment variable is required")
        
        keys = [key.strip() for key in self.api_keys.split(",")]
        return [key for key in keys if key]  # 过滤空字符串


# 全局配置实例
settings = Settings()
