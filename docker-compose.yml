version: '3.8'

services:
  gemini-api-proxy:
    build: .
    ports:
      - "8080:8080"
    environment:
      - HOST=0.0.0.0
      - PORT=8080
      - API_KEYS=${API_KEYS}
      - TARGET_BASE_URL=${TARGET_BASE_URL:-https://api.openai.com}
      - TIMEOUT=${TIMEOUT:-60}
      - MAX_RETRIES=${MAX_RETRIES:-3}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENABLE_CORS=${ENABLE_CORS:-true}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - proxy-network

networks:
  proxy-network:
    driver: bridge
