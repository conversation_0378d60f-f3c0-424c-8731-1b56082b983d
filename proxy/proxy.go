package proxy

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync/atomic"
	"time"

	"gemini-api-proxy/config"
	"gemini-api-proxy/models"

	"github.com/gin-gonic/gin"
)

// ProxyService 代理服务
type ProxyService struct {
	config     *config.Config
	keyManager *KeyManager
	client     *http.Client
	stats      *ProxyStats
	startTime  time.Time
}

// ProxyStats 代理统计
type ProxyStats struct {
	TotalRequests   int64
	SuccessRequests int64
	FailedRequests  int64
	TotalLatency    int64 // 总延迟（毫秒）
}

// NewProxyService 创建新的代理服务
func NewProxyService(cfg *config.Config) *ProxyService {
	return &ProxyService{
		config:     cfg,
		keyManager: NewKeyManager(cfg.APIKeys),
		client: &http.Client{
			Timeout: cfg.Timeout,
		},
		stats:     &ProxyStats{},
		startTime: time.Now(),
	}
}

// ProxyRequest 代理请求处理
func (ps *ProxyService) ProxyRequest(c *gin.Context) {
	startTime := time.Now()
	atomic.AddInt64(&ps.stats.TotalRequests, 1)

	// 读取请求体
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		ps.handleError(c, http.StatusBadRequest, "Failed to read request body", err)
		return
	}

	// 获取API密钥
	apiKey := ps.keyManager.GetNextKey()
	if apiKey == "" {
		ps.handleError(c, http.StatusInternalServerError, "No API key available", nil)
		return
	}

	// 构建目标URL - 支持Gemini API的特殊URL结构
	targetURL := ps.buildTargetURL(c.Request.URL.Path, c.Request.URL.RawQuery, apiKey)

	// 创建新请求
	req, err := http.NewRequest(c.Request.Method, targetURL, bytes.NewReader(body))
	if err != nil {
		ps.handleError(c, http.StatusInternalServerError, "Failed to create request", err)
		ps.keyManager.RecordFailure(apiKey)
		return
	}

	// 复制请求头
	ps.copyHeaders(c.Request, req)

	// 根据目标API类型设置认证
	ps.setAuthentication(req, apiKey)

	// 发送请求
	resp, err := ps.client.Do(req)
	if err != nil {
		ps.handleError(c, http.StatusBadGateway, "Failed to proxy request", err)
		ps.keyManager.RecordFailure(apiKey)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		ps.handleError(c, http.StatusBadGateway, "Failed to read response", err)
		ps.keyManager.RecordFailure(apiKey)
		return
	}

	// 记录统计信息
	latency := time.Since(startTime).Milliseconds()
	atomic.AddInt64(&ps.stats.TotalLatency, latency)

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		atomic.AddInt64(&ps.stats.SuccessRequests, 1)
		ps.keyManager.RecordSuccess(apiKey)
	} else {
		atomic.AddInt64(&ps.stats.FailedRequests, 1)
		ps.keyManager.RecordFailure(apiKey)
	}

	// 复制响应头
	ps.copyResponseHeaders(resp, c)

	// 返回响应
	c.Data(resp.StatusCode, resp.Header.Get("Content-Type"), respBody)
}

// GetStats 获取统计信息
func (ps *ProxyService) GetStats(c *gin.Context) {
	keyStats := ps.keyManager.GetStats()
	keyStatsInfo := make(map[string]*models.KeyStatsInfo)

	for key, stats := range keyStats {
		successRate := 0.0
		if stats.TotalRequests > 0 {
			successRate = float64(stats.SuccessRequests) / float64(stats.TotalRequests) * 100
		}

		// 隐藏API密钥的敏感部分
		maskedKey := ps.maskAPIKey(key)
		keyStatsInfo[maskedKey] = &models.KeyStatsInfo{
			TotalRequests:   stats.TotalRequests,
			SuccessRequests: stats.SuccessRequests,
			FailedRequests:  stats.FailedRequests,
			SuccessRate:     successRate,
			LastUsed:        stats.LastUsed,
			IsHealthy:       stats.IsHealthy,
		}
	}

	avgLatency := 0.0
	totalReqs := atomic.LoadInt64(&ps.stats.TotalRequests)
	if totalReqs > 0 {
		avgLatency = float64(atomic.LoadInt64(&ps.stats.TotalLatency)) / float64(totalReqs)
	}

	response := &models.ProxyStats{
		TotalRequests:   atomic.LoadInt64(&ps.stats.TotalRequests),
		SuccessRequests: atomic.LoadInt64(&ps.stats.SuccessRequests),
		FailedRequests:  atomic.LoadInt64(&ps.stats.FailedRequests),
		AverageLatency:  avgLatency,
		KeyStats:        keyStatsInfo,
		Uptime:          time.Since(ps.startTime).String(),
		LastUpdated:     time.Now(),
	}

	c.JSON(http.StatusOK, response)
}

// Health 健康检查
func (ps *ProxyService) Health(c *gin.Context) {
	response := &models.HealthResponse{
		Status:    "healthy",
		Timestamp: time.Now(),
		Version:   "1.0.0",
		Uptime:    time.Since(ps.startTime).String(),
	}
	c.JSON(http.StatusOK, response)
}

// copyHeaders 复制请求头
func (ps *ProxyService) copyHeaders(src *http.Request, dst *http.Request) {
	for key, values := range src.Header {
		// 跳过一些不应该转发的头
		if ps.shouldSkipHeader(key) {
			continue
		}
		for _, value := range values {
			dst.Header.Add(key, value)
		}
	}
}

// copyResponseHeaders 复制响应头
func (ps *ProxyService) copyResponseHeaders(src *http.Response, dst *gin.Context) {
	for key, values := range src.Header {
		// 跳过一些不应该转发的头
		if ps.shouldSkipResponseHeader(key) {
			continue
		}
		for _, value := range values {
			dst.Header(key, value)
		}
	}
}

// shouldSkipHeader 判断是否应该跳过某个请求头
func (ps *ProxyService) shouldSkipHeader(key string) bool {
	key = strings.ToLower(key)
	skipHeaders := []string{
		"authorization",
		"host",
		"content-length",
		"connection",
		"upgrade",
		"proxy-connection",
		"proxy-authenticate",
		"proxy-authorization",
		"te",
		"trailers",
		"transfer-encoding",
	}

	for _, skip := range skipHeaders {
		if key == skip {
			return true
		}
	}
	return false
}

// shouldSkipResponseHeader 判断是否应该跳过某个响应头
func (ps *ProxyService) shouldSkipResponseHeader(key string) bool {
	key = strings.ToLower(key)
	skipHeaders := []string{
		"connection",
		"upgrade",
		"proxy-connection",
		"proxy-authenticate",
		"proxy-authorization",
		"te",
		"trailers",
		"transfer-encoding",
	}

	for _, skip := range skipHeaders {
		if key == skip {
			return true
		}
	}
	return false
}

// handleError 处理错误
func (ps *ProxyService) handleError(c *gin.Context, statusCode int, message string, err error) {
	atomic.AddInt64(&ps.stats.FailedRequests, 1)

	errorMsg := message
	if err != nil {
		errorMsg = fmt.Sprintf("%s: %v", message, err)
	}

	response := &models.ErrorResponse{
		Error: models.ErrorDetail{
			Message: errorMsg,
			Type:    "proxy_error",
		},
	}

	c.JSON(statusCode, response)
}

// maskAPIKey 隐藏API密钥的敏感部分
func (ps *ProxyService) maskAPIKey(key string) string {
	if len(key) <= 8 {
		return "****"
	}
	return key[:4] + "****" + key[len(key)-4:]
}

// buildTargetURL 构建目标URL，支持不同API的URL结构
func (ps *ProxyService) buildTargetURL(path, query, apiKey string) string {
	baseURL := ps.config.TargetBaseURL

	// 检查是否是Gemini API
	if ps.isGeminiAPI() {
		// Gemini API需要将API密钥作为查询参数
		targetURL := baseURL + path

		// 添加API密钥到查询参数
		if query != "" {
			targetURL += "?" + query + "&key=" + apiKey
		} else {
			targetURL += "?key=" + apiKey
		}

		return targetURL
	}

	// OpenAI API或其他API
	targetURL := baseURL + path
	if query != "" {
		targetURL += "?" + query
	}

	return targetURL
}

// setAuthentication 根据API类型设置认证
func (ps *ProxyService) setAuthentication(req *http.Request, apiKey string) {
	if ps.isGeminiAPI() {
		// Gemini API使用查询参数认证，不需要设置Authorization头
		return
	}

	// OpenAI API或其他API使用Authorization头
	req.Header.Set("Authorization", "Bearer "+apiKey)
}

// isGeminiAPI 检查是否是Gemini API
func (ps *ProxyService) isGeminiAPI() bool {
	return strings.Contains(ps.config.TargetBaseURL, "generativelanguage.googleapis.com")
}
