package proxy

import (
	"sync"
	"sync/atomic"
	"time"
)

// KeyManager API密钥管理器
type KeyManager struct {
	keys        []string
	currentIdx  int64
	mutex       sync.RWMutex
	keyStats    map[string]*KeyStats
	healthCheck bool
}

// KeyStats 密钥统计信息
type KeyStats struct {
	TotalRequests   int64
	SuccessRequests int64
	FailedRequests  int64
	LastUsed        time.Time
	IsHealthy       bool
	mutex           sync.RWMutex
}

// NewKeyManager 创建新的密钥管理器
func NewKeyManager(keys []string) *KeyManager {
	km := &KeyManager{
		keys:        make([]string, len(keys)),
		keyStats:    make(map[string]*KeyStats),
		healthCheck: true,
	}

	copy(km.keys, keys)

	// 初始化密钥统计
	for _, key := range keys {
		km.keyStats[key] = &KeyStats{
			IsHealthy: true,
			LastUsed:  time.Now(),
		}
	}

	return km
}

// GetNextKey 获取下一个可用的API密钥（轮询方式）
func (km *KeyManager) GetNextKey() string {
	km.mutex.RLock()
	defer km.mutex.RUnlock()

	if len(km.keys) == 0 {
		return ""
	}

	// 如果启用健康检查，优先选择健康的密钥
	if km.healthCheck {
		for i := 0; i < len(km.keys); i++ {
			idx := atomic.AddInt64(&km.currentIdx, 1) % int64(len(km.keys))
			key := km.keys[idx]
			if stats, exists := km.keyStats[key]; exists && stats.IsHealthy {
				km.updateLastUsed(key)
				return key
			}
		}
	}

	// 如果没有健康的密钥或未启用健康检查，使用轮询
	idx := atomic.AddInt64(&km.currentIdx, 1) % int64(len(km.keys))
	key := km.keys[idx]
	km.updateLastUsed(key)
	return key
}

// GetRandomKey 获取随机API密钥
func (km *KeyManager) GetRandomKey() string {
	km.mutex.RLock()
	defer km.mutex.RUnlock()

	if len(km.keys) == 0 {
		return ""
	}

	// 简单的伪随机选择（基于当前时间）
	idx := time.Now().UnixNano() % int64(len(km.keys))
	key := km.keys[idx]
	km.updateLastUsed(key)
	return key
}

// RecordSuccess 记录成功请求
func (km *KeyManager) RecordSuccess(key string) {
	if stats, exists := km.keyStats[key]; exists {
		stats.mutex.Lock()
		atomic.AddInt64(&stats.TotalRequests, 1)
		atomic.AddInt64(&stats.SuccessRequests, 1)
		stats.IsHealthy = true
		stats.mutex.Unlock()
	}
}

// RecordFailure 记录失败请求
func (km *KeyManager) RecordFailure(key string) {
	if stats, exists := km.keyStats[key]; exists {
		stats.mutex.Lock()
		atomic.AddInt64(&stats.TotalRequests, 1)
		atomic.AddInt64(&stats.FailedRequests, 1)
		
		// 如果失败率过高，标记为不健康
		if stats.TotalRequests > 10 {
			failureRate := float64(stats.FailedRequests) / float64(stats.TotalRequests)
			if failureRate > 0.5 { // 失败率超过50%
				stats.IsHealthy = false
			}
		}
		stats.mutex.Unlock()
	}
}

// GetStats 获取所有密钥的统计信息
func (km *KeyManager) GetStats() map[string]*KeyStats {
	km.mutex.RLock()
	defer km.mutex.RUnlock()

	result := make(map[string]*KeyStats)
	for key, stats := range km.keyStats {
		// 创建副本以避免并发问题
		statsCopy := &KeyStats{
			TotalRequests:   atomic.LoadInt64(&stats.TotalRequests),
			SuccessRequests: atomic.LoadInt64(&stats.SuccessRequests),
			FailedRequests:  atomic.LoadInt64(&stats.FailedRequests),
			LastUsed:        stats.LastUsed,
			IsHealthy:       stats.IsHealthy,
		}
		result[key] = statsCopy
	}
	return result
}

// updateLastUsed 更新密钥最后使用时间
func (km *KeyManager) updateLastUsed(key string) {
	if stats, exists := km.keyStats[key]; exists {
		stats.mutex.Lock()
		stats.LastUsed = time.Now()
		stats.mutex.Unlock()
	}
}

// SetHealthCheck 设置是否启用健康检查
func (km *KeyManager) SetHealthCheck(enabled bool) {
	km.mutex.Lock()
	km.healthCheck = enabled
	km.mutex.Unlock()
}

// ResetKeyHealth 重置密钥健康状态
func (km *KeyManager) ResetKeyHealth(key string) {
	if stats, exists := km.keyStats[key]; exists {
		stats.mutex.Lock()
		stats.IsHealthy = true
		stats.mutex.Unlock()
	}
}
