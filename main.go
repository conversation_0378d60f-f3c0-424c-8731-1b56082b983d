package main

import (
	"log"
	"net/http"

	"gemini-api-proxy/config"
	"gemini-api-proxy/middleware"
	"gemini-api-proxy/proxy"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 设置Gin模式
	if cfg.LogLevel == "DEBUG" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(middleware.Recovery())
	r.Use(middleware.Logger())
	r.Use(middleware.RequestID())

	if cfg.EnableCORS {
		r.Use(middleware.CORS())
	}

	// 创建代理服务
	proxyService := proxy.NewProxyService(cfg)

	// 设置路由
	setupRoutes(r, proxyService, cfg)

	// 启动服务器
	addr := cfg.Host + ":" + cfg.Port
	log.Printf("Starting API proxy server on %s", addr)
	log.Printf("Target API: %s", cfg.TargetBaseURL)
	log.Printf("API Keys configured: %d", len(cfg.APIKeys))

	if err := r.Run(addr); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

// setupRoutes 设置路由
func setupRoutes(r *gin.Engine, proxyService *proxy.ProxyService, cfg *config.Config) {
	// 健康检查（无需认证）
	r.GET("/health", proxyService.Health)

	// 统计信息（无需认证）
	r.GET("/stats", proxyService.GetStats)

	// 需要认证的API路由组
	apiGroup := r.Group("/")
	if cfg.AuthKey != "" {
		apiGroup.Use(middleware.AuthMiddleware(cfg.AuthKey))
	}

	// OpenAI兼容的API路由
	v1 := apiGroup.Group("/v1")
	{
		// Chat completions
		v1.POST("/chat/completions", proxyService.ProxyRequest)

		// Text completions
		v1.POST("/completions", proxyService.ProxyRequest)

		// Embeddings
		v1.POST("/embeddings", proxyService.ProxyRequest)

		// Models
		v1.GET("/models", proxyService.ProxyRequest)
		v1.GET("/models/:model", proxyService.ProxyRequest)

		// Files
		v1.GET("/files", proxyService.ProxyRequest)
		v1.POST("/files", proxyService.ProxyRequest)
		v1.GET("/files/:file_id", proxyService.ProxyRequest)
		v1.DELETE("/files/:file_id", proxyService.ProxyRequest)

		// Fine-tuning
		v1.POST("/fine-tuning/jobs", proxyService.ProxyRequest)
		v1.GET("/fine-tuning/jobs", proxyService.ProxyRequest)
		v1.GET("/fine-tuning/jobs/:job_id", proxyService.ProxyRequest)
		v1.POST("/fine-tuning/jobs/:job_id/cancel", proxyService.ProxyRequest)

		// Images
		v1.POST("/images/generations", proxyService.ProxyRequest)
		v1.POST("/images/edits", proxyService.ProxyRequest)
		v1.POST("/images/variations", proxyService.ProxyRequest)

		// Audio
		v1.POST("/audio/transcriptions", proxyService.ProxyRequest)
		v1.POST("/audio/translations", proxyService.ProxyRequest)
		v1.POST("/audio/speech", proxyService.ProxyRequest)

		// Moderations
		v1.POST("/moderations", proxyService.ProxyRequest)
	}

	// Gemini API路由
	v1beta := apiGroup.Group("/v1beta")
	{
		// Gemini models
		v1beta.POST("/models/*path", proxyService.ProxyRequest)
		v1beta.GET("/models/*path", proxyService.ProxyRequest)
	}

	// 根路径重定向到健康检查
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Gemini API Proxy Server",
			"version": "1.0.0",
			"status":  "running",
			"endpoints": gin.H{
				"health": "/health",
				"stats":  "/stats",
				"api":    "/v1/*",
			},
		})
	})

	// 捕获所有其他路由并代理（需要认证）
	r.NoRoute(func(c *gin.Context) {
		path := c.Request.URL.Path

		// 跳过不需要认证的路径
		if path == "/health" || path == "/stats" || path == "/" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": gin.H{
					"message": "Endpoint not found",
					"type":    "not_found",
				},
			})
			return
		}

		// 对于API路径，先进行认证检查
		if (len(path) >= 3 && path[:3] == "/v1") ||
			(len(path) >= 6 && path[:6] == "/v1beta") {
			// 如果配置了认证密钥，进行认证检查
			if cfg.AuthKey != "" {
				middleware.AuthMiddleware(cfg.AuthKey)(c)
				if c.IsAborted() {
					return
				}
			}
			proxyService.ProxyRequest(c)
		} else {
			c.JSON(http.StatusNotFound, gin.H{
				"error": gin.H{
					"message": "Endpoint not found",
					"type":    "not_found",
				},
			})
		}
	})
}
