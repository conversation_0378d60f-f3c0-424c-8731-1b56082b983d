package main

import (
	"log"
	"net/http"

	"gemini-api-proxy/config"
	"gemini-api-proxy/middleware"
	"gemini-api-proxy/proxy"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 设置Gin模式
	if cfg.LogLevel == "DEBUG" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建Gin引擎
	r := gin.New()

	// 添加中间件
	r.Use(middleware.Recovery())
	r.Use(middleware.Logger())
	r.Use(middleware.RequestID())

	if cfg.EnableCORS {
		r.Use(middleware.CORS())
	}

	// 创建代理服务
	proxyService := proxy.NewProxyService(cfg)

	// 设置路由
	setupRoutes(r, proxyService)

	// 启动服务器
	addr := cfg.Host + ":" + cfg.Port
	log.Printf("Starting API proxy server on %s", addr)
	log.Printf("Target API: %s", cfg.TargetBaseURL)
	log.Printf("API Keys configured: %d", len(cfg.APIKeys))

	if err := r.Run(addr); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}

// setupRoutes 设置路由
func setupRoutes(r *gin.Engine, proxyService *proxy.ProxyService) {
	// 健康检查
	r.GET("/health", proxyService.Health)

	// 统计信息
	r.GET("/stats", proxyService.GetStats)

	// OpenAI兼容的API路由
	v1 := r.Group("/v1")
	{
		// Chat completions
		v1.POST("/chat/completions", proxyService.ProxyRequest)
		
		// Text completions
		v1.POST("/completions", proxyService.ProxyRequest)
		
		// Embeddings
		v1.POST("/embeddings", proxyService.ProxyRequest)
		
		// Models
		v1.GET("/models", proxyService.ProxyRequest)
		v1.GET("/models/:model", proxyService.ProxyRequest)
		
		// Files
		v1.GET("/files", proxyService.ProxyRequest)
		v1.POST("/files", proxyService.ProxyRequest)
		v1.GET("/files/:file_id", proxyService.ProxyRequest)
		v1.DELETE("/files/:file_id", proxyService.ProxyRequest)
		
		// Fine-tuning
		v1.POST("/fine-tuning/jobs", proxyService.ProxyRequest)
		v1.GET("/fine-tuning/jobs", proxyService.ProxyRequest)
		v1.GET("/fine-tuning/jobs/:job_id", proxyService.ProxyRequest)
		v1.POST("/fine-tuning/jobs/:job_id/cancel", proxyService.ProxyRequest)
		
		// Images
		v1.POST("/images/generations", proxyService.ProxyRequest)
		v1.POST("/images/edits", proxyService.ProxyRequest)
		v1.POST("/images/variations", proxyService.ProxyRequest)
		
		// Audio
		v1.POST("/audio/transcriptions", proxyService.ProxyRequest)
		v1.POST("/audio/translations", proxyService.ProxyRequest)
		v1.POST("/audio/speech", proxyService.ProxyRequest)
		
		// Moderations
		v1.POST("/moderations", proxyService.ProxyRequest)
	}

	// 根路径重定向到健康检查
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"message": "Gemini API Proxy Server",
			"version": "1.0.0",
			"status":  "running",
			"endpoints": gin.H{
				"health": "/health",
				"stats":  "/stats",
				"api":    "/v1/*",
			},
		})
	})

	// 捕获所有其他路由并代理
	r.NoRoute(func(c *gin.Context) {
		// 只代理以/v1开头的路径
		if len(c.Request.URL.Path) >= 3 && c.Request.URL.Path[:3] == "/v1" {
			proxyService.ProxyRequest(c)
		} else {
			c.JSON(http.StatusNotFound, gin.H{
				"error": gin.H{
					"message": "Endpoint not found",
					"type":    "not_found",
				},
			})
		}
	})
}
