package config

import (
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Config 应用配置结构
type Config struct {
	// 服务配置
	Host string
	Port string

	// API密钥配置
	APIKeys []string

	// 目标API服务配置
	TargetBaseURL string

	// 代理配置
	Timeout    time.Duration
	MaxRetries int

	// 日志配置
	LogLevel string

	// CORS配置
	EnableCORS bool
}

// Load 加载配置
func Load() *Config {
	// 尝试加载.env文件
	if err := godotenv.Load(); err != nil {
		log.Println("Warning: .env file not found, using environment variables")
	}

	config := &Config{
		Host:          getEnv("HOST", "0.0.0.0"),
		Port:          getEnv("PORT", "8080"),
		TargetBaseURL: getEnv("TARGET_BASE_URL", "https://api.openai.com"),
		LogLevel:      getEnv("LOG_LEVEL", "INFO"),
		EnableCORS:    getEnvBool("ENABLE_CORS", true),
	}

	// 解析API密钥
	apiKeysStr := getEnv("API_KEYS", "")
	if apiKeysStr == "" {
		log.Fatal("API_KEYS environment variable is required")
	}

	keys := strings.Split(apiKeysStr, ",")
	for _, key := range keys {
		key = strings.TrimSpace(key)
		if key != "" {
			config.APIKeys = append(config.APIKeys, key)
		}
	}

	if len(config.APIKeys) == 0 {
		log.Fatal("At least one API key is required")
	}

	// 解析超时时间
	timeoutStr := getEnv("TIMEOUT", "60")
	if timeout, err := strconv.Atoi(timeoutStr); err != nil {
		log.Printf("Invalid TIMEOUT value: %s, using default 60s", timeoutStr)
		config.Timeout = 60 * time.Second
	} else {
		config.Timeout = time.Duration(timeout) * time.Second
	}

	// 解析最大重试次数
	maxRetriesStr := getEnv("MAX_RETRIES", "3")
	if maxRetries, err := strconv.Atoi(maxRetriesStr); err != nil {
		log.Printf("Invalid MAX_RETRIES value: %s, using default 3", maxRetriesStr)
		config.MaxRetries = 3
	} else {
		config.MaxRetries = maxRetries
	}

	return config
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvBool 获取布尔类型的环境变量
func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if parsed, err := strconv.ParseBool(value); err == nil {
			return parsed
		}
	}
	return defaultValue
}
