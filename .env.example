# 服务配置
HOST=0.0.0.0
PORT=8080

# API密钥配置（多个密钥用逗号分隔）
API_KEYS=sk-your-api-key-1,sk-your-api-key-2,sk-your-api-key-3

# 目标API服务配置
TARGET_BASE_URL=https://api.openai.com

# 代理配置
TIMEOUT=60
MAX_RETRIES=3

# 日志配置
LOG_LEVEL=INFO

# CORS配置
ENABLE_CORS=true

# 自定义认证密钥（可选，如果设置则需要在请求中携带此密钥）
# 支持三种方式携带：
# 1. Authorization头: Authorization: Bearer your-auth-key
# 2. X-API-Key头: X-API-Key: your-auth-key
# 3. 查询参数: ?auth_key=your-auth-key
AUTH_KEY=your-secret-auth-key
